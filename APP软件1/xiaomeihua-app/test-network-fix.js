#!/usr/bin/env node

/**
 * 网络修复测试脚本
 * 用于测试卡密验证的网络连接修复效果
 */

const axios = require('axios');

// 服务器配置（与main.js保持一致）
const SERVER_CONFIG = {
  main: 'https://xiaomeihuakefu.cn',
  backup: 'https://xiaomeihuakefu.cn', // 使用主服务器作为备用
  timeout: 10000,
  retries: 2
};

// 测试用的卡密（无效卡密，用于测试网络连接）
const TEST_LICENSE_KEY = 'TEST-INVALID-KEY-FOR-NETWORK-TEST';

// 网络请求函数（简化版）
async function makeRequest(url, data, options = {}) {
  const { timeout = SERVER_CONFIG.timeout, retries = SERVER_CONFIG.retries } = options;
  let lastError = null;
  
  for (let i = 0; i <= retries; i++) {
    try {
      console.log(`尝试请求 ${url} (尝试 ${i+1}/${retries+1})`);
      
      const params = new URLSearchParams();
      for (const key in data) {
        params.append(key, data[key]);
      }
      const postData = params.toString();
      
      const response = await axios.post(url, postData, { 
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout,
        maxContentLength: 10 * 1024 * 1024,
        maxBodyLength: 10 * 1024 * 1024
      });
      return response;
    } catch (error) {
      console.error(`请求失败 (尝试 ${i+1}/${retries+1}):`, error.message);
      lastError = error;
      
      if (i < retries) {
        const delay = 500;
        console.log(`等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError;
}

// 网络连接诊断功能
async function diagnoseNetworkConnection() {
  const results = {
    mainServer: false,
    backupServer: false,
    internetConnection: false,
    dnsResolution: false
  };

  try {
    console.log('诊断：测试主服务器连接...');
    // 使用POST请求测试API端点，因为verify.php只接受POST
    const testData = new URLSearchParams();
    testData.append('key', 'TEST-DIAGNOSIS-KEY');

    const mainResponse = await axios.post(`${SERVER_CONFIG.main}/api/verify.php`, testData, {
      timeout: 5000,
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      validateStatus: () => true
    });
    results.mainServer = mainResponse.status < 500;
    console.log(`主服务器状态: ${mainResponse.status}`);
    console.log(`主服务器响应: ${JSON.stringify(mainResponse.data)}`);
  } catch (error) {
    console.log('主服务器连接失败:', error.message);
  }

  try {
    console.log('诊断：测试互联网连接...');
    const internetResponse = await axios.get('https://www.baidu.com', { 
      timeout: 5000,
      validateStatus: () => true
    });
    results.internetConnection = internetResponse.status < 500;
    console.log(`互联网连接状态: ${internetResponse.status}`);
  } catch (error) {
    console.log('互联网连接失败:', error.message);
  }

  try {
    console.log('诊断：测试DNS解析...');
    const dns = require('dns').promises;
    await dns.lookup('xiaomeihuakefu.cn');
    results.dnsResolution = true;
    console.log('DNS解析成功');
  } catch (error) {
    console.log('DNS解析失败:', error.message);
  }

  return results;
}

// 生成诊断报告
function generateNetworkDiagnosisReport(results) {
  let report = '网络连接诊断报告：\n\n';
  
  if (!results.internetConnection) {
    report += '❌ 互联网连接：失败\n';
    report += '   建议：请检查网络连接，确保设备已连接到互联网\n\n';
  } else {
    report += '✅ 互联网连接：正常\n\n';
  }

  if (!results.dnsResolution) {
    report += '❌ DNS解析：失败\n';
    report += '   建议：请检查DNS设置，或尝试更换DNS服务器\n\n';
  } else {
    report += '✅ DNS解析：正常\n\n';
  }

  if (!results.mainServer) {
    report += '❌ 主服务器连接：失败\n';
    report += '   建议：服务器可能正在维护，请稍后重试\n\n';
  } else {
    report += '✅ 主服务器连接：正常\n\n';
  }

  return report;
}

// 测试卡密验证网络连接
async function testLicenseVerification() {
  console.log('=== 开始测试卡密验证网络连接 ===\n');

  // 测试多个可能的API端点
  const testEndpoints = [
    `${SERVER_CONFIG.main}/api/verify.php`,
    `${SERVER_CONFIG.main}/deploy/verify.php`,
    `${SERVER_CONFIG.main}/verify.php`
  ];

  for (let i = 0; i < testEndpoints.length; i++) {
    const mainUrl = testEndpoints[i];
    console.log(`${i + 1}. 测试端点: ${mainUrl}`);

    try {
    
      const response = await makeRequest(mainUrl, {
        key: TEST_LICENSE_KEY,
        device_id: 'test-device-id',
        version: '1.0.13',
        device_name: 'Test-Device',
        platform: 'test',
        check_concurrent_login: 1
      });

      console.log(`✅ 端点 ${mainUrl} 连接成功`);
      console.log('响应状态:', response.status);
      console.log('响应数据:', response.data);

      // 找到可用的端点，更新配置建议
      console.log(`\n🎉 找到可用的API端点: ${mainUrl}`);
      return {
        success: true,
        message: '网络连接测试通过',
        workingEndpoint: mainUrl
      };

    } catch (endpointError) {
      console.log(`❌ 端点 ${mainUrl} 连接失败:`, endpointError.message);

      // 如果是最后一个端点，进行网络诊断
      if (i === testEndpoints.length - 1) {
        console.log('\n进行网络连接诊断...');
        const diagnosisResults = await diagnoseNetworkConnection();
        const diagnosisReport = generateNetworkDiagnosisReport(diagnosisResults);

        console.log('\n诊断结果:');
        console.log(diagnosisReport);

        return {
          success: false,
          message: '所有API端点都无法连接',
          diagnosis: diagnosisResults,
          report: diagnosisReport,
          testedEndpoints: testEndpoints
        };
      }
    }
  }

  // 如果所有端点都失败了，这里不应该到达
  console.log('\n=== 网络连接测试完成 ===');
  return {
    success: false,
    message: '所有API端点测试完成，但没有找到可用的端点'
  };
}

// 主函数
async function main() {
  console.log('网络修复效果测试脚本');
  console.log('========================\n');
  
  const result = await testLicenseVerification();
  
  console.log('\n最终结果:');
  console.log('成功:', result.success);
  console.log('消息:', result.message);
  
  if (result.diagnosis) {
    console.log('\n诊断信息:');
    console.log('主服务器:', result.diagnosis.mainServer ? '✅ 正常' : '❌ 失败');
    console.log('互联网连接:', result.diagnosis.internetConnection ? '✅ 正常' : '❌ 失败');
    console.log('DNS解析:', result.diagnosis.dnsResolution ? '✅ 正常' : '❌ 失败');
  }
  
  process.exit(result.success ? 0 : 1);
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testLicenseVerification,
  diagnoseNetworkConnection,
  generateNetworkDiagnosisReport
};
