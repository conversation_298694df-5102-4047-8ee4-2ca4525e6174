#!/usr/bin/env node

/**
 * 最终修复效果测试脚本
 * 测试使用正确API端点的卡密验证功能
 */

const axios = require('axios');

// 服务器配置（与修复后的main.js保持一致）
const SERVER_CONFIG = {
  main: 'https://xiaomeihuakefu.cn',
  backup: 'https://xiaomeihuakefu.cn',
  timeout: 10000,
  retries: 2
};

// 测试用的卡密（无效卡密，用于测试网络连接）
const TEST_LICENSE_KEY = 'TEST-INVALID-KEY-FOR-NETWORK-TEST';

// 网络请求函数（与main.js保持一致）
async function makeRequest(url, data, options = {}) {
  const { timeout = SERVER_CONFIG.timeout, retries = SERVER_CONFIG.retries } = options;
  let lastError = null;
  
  for (let i = 0; i <= retries; i++) {
    try {
      console.log(`尝试请求 ${url} (尝试 ${i+1}/${retries+1})`);
      
      const params = new URLSearchParams();
      for (const key in data) {
        params.append(key, data[key]);
      }
      const postData = params.toString();
      
      const response = await axios.post(url, postData, { 
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout,
        maxContentLength: 10 * 1024 * 1024,
        maxBodyLength: 10 * 1024 * 1024
      });
      return response;
    } catch (error) {
      console.error(`请求失败 (尝试 ${i+1}/${retries+1}):`, error.message);
      lastError = error;
      
      if (i < retries) {
        const delay = 500;
        console.log(`等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError;
}

// 测试修复后的卡密验证
async function testFixedLicenseVerification() {
  console.log('=== 测试修复后的卡密验证功能 ===\n');
  
  try {
    // 测试主服务器（使用正确的端点）
    console.log('1. 测试主服务器连接...');
    const mainUrl = `${SERVER_CONFIG.main}/deploy/verify.php`;
    
    try {
      const response = await makeRequest(mainUrl, {
        key: TEST_LICENSE_KEY,
        device_id: 'test-device-id',
        version: '1.0.13',
        device_name: 'Test-Device',
        platform: 'test',
        check_concurrent_login: 1
      });
      
      console.log('✅ 主服务器连接成功');
      console.log('响应状态:', response.status);
      
      // 检查响应内容
      if (response.data) {
        try {
          const jsonData = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;
          console.log('响应数据:', jsonData);
          
          if (jsonData.success === false && jsonData.message) {
            console.log('✅ API正常工作，返回了预期的错误消息:', jsonData.message);
            return {
              success: true,
              message: 'API端点工作正常，能够正确处理请求',
              endpoint: mainUrl,
              apiResponse: jsonData
            };
          }
        } catch (parseError) {
          // 如果不是JSON，检查是否是HTML错误页面
          if (typeof response.data === 'string' && response.data.includes('Fatal error')) {
            console.log('⚠️ 服务器端有PHP错误，但端点可达');
            return {
              success: false,
              message: '服务器端存在PHP错误，需要修复服务器代码',
              endpoint: mainUrl,
              error: 'PHP Fatal Error detected'
            };
          }
        }
      }
      
      return {
        success: true,
        message: '网络连接正常，API端点可达',
        endpoint: mainUrl
      };
      
    } catch (mainError) {
      console.log('❌ 主服务器连接失败:', mainError.message);
      
      return {
        success: false,
        message: `主服务器连接失败: ${mainError.message}`,
        endpoint: mainUrl,
        error: mainError.message
      };
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
    return {
      success: false,
      message: '测试失败: ' + error.message
    };
  }
}

// 主函数
async function main() {
  console.log('卡密验证网络修复效果测试');
  console.log('================================\n');
  
  const result = await testFixedLicenseVerification();
  
  console.log('\n=== 测试结果 ===');
  console.log('成功:', result.success ? '✅ 是' : '❌ 否');
  console.log('消息:', result.message);
  
  if (result.endpoint) {
    console.log('测试端点:', result.endpoint);
  }
  
  if (result.apiResponse) {
    console.log('API响应:', JSON.stringify(result.apiResponse, null, 2));
  }
  
  if (result.error) {
    console.log('错误详情:', result.error);
  }
  
  console.log('\n=== 修复总结 ===');
  console.log('1. ✅ 发现并修复了API端点路径错误');
  console.log('2. ✅ 从 /api/verify.php 更正为 /deploy/verify.php');
  console.log('3. ✅ 增强了网络错误处理和诊断功能');
  console.log('4. ✅ 提供了详细的错误信息和解决建议');
  
  if (result.success) {
    console.log('5. ✅ 网络连接问题已解决，用户应该能够正常登录');
  } else {
    console.log('5. ⚠️ 仍需要修复服务器端的PHP错误');
  }
  
  process.exit(result.success ? 0 : 1);
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testFixedLicenseVerification,
  makeRequest
};
