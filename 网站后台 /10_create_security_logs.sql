-- 小梅花AI客服助手 - 安全日志表创建脚本
-- 版本: v1.0.0 (MySQL 5.7+ 兼容版)
-- 创建时间: 2024-12-19
-- 兼容性: 专为MySQL 5.7+ 优化，支持宝塔环境

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建安全日志表
CREATE TABLE IF NOT EXISTS `security_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key` varchar(50) DEFAULT NULL COMMENT '卡密（部分）',
  `error_code` varchar(50) DEFAULT NULL COMMENT '错误代码',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_license_key` (`license_key`),
  KEY `idx_error_code` (`error_code`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全日志表';

-- 检查license_keys表是否存在，如果不存在则跳过后续操作
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables
                     WHERE table_schema = DATABASE()
                     AND table_name = 'license_keys');

-- 为license_keys表添加新字段（兼容性处理）
SET @sql = (SELECT IF(@table_exists > 0,
    'SELECT "开始为license_keys表添加字段" as message',
    'SELECT "license_keys表不存在，跳过字段添加" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加last_used_at字段（如果表存在且字段不存在）
SET @sql = (SELECT IF(
    @table_exists > 0 AND
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = DATABASE()
     AND table_name = 'license_keys'
     AND column_name = 'last_used_at') = 0,
    'ALTER TABLE `license_keys` ADD COLUMN `last_used_at` timestamp NULL DEFAULT NULL COMMENT "最后使用时间"',
    'SELECT "last_used_at字段已存在或表不存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加last_heartbeat字段（如果表存在且字段不存在）
SET @sql = (SELECT IF(
    @table_exists > 0 AND
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = DATABASE()
     AND table_name = 'license_keys'
     AND column_name = 'last_heartbeat') = 0,
    'ALTER TABLE `license_keys` ADD COLUMN `last_heartbeat` timestamp NULL DEFAULT NULL COMMENT "最后心跳时间"',
    'SELECT "last_heartbeat字段已存在或表不存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加last_used_ip字段（如果表存在且字段不存在）
SET @sql = (SELECT IF(
    @table_exists > 0 AND
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = DATABASE()
     AND table_name = 'license_keys'
     AND column_name = 'last_used_ip') = 0,
    'ALTER TABLE `license_keys` ADD COLUMN `last_used_ip` varchar(45) DEFAULT NULL COMMENT "最后使用IP"',
    'SELECT "last_used_ip字段已存在或表不存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（兼容性处理）
-- 添加last_used_at索引
SET @sql = (SELECT IF(
    @table_exists > 0 AND
    (SELECT COUNT(*) FROM information_schema.statistics
     WHERE table_schema = DATABASE()
     AND table_name = 'license_keys'
     AND index_name = 'idx_last_used_at') = 0,
    'ALTER TABLE `license_keys` ADD INDEX `idx_last_used_at` (`last_used_at`)',
    'SELECT "idx_last_used_at索引已存在或表不存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加last_heartbeat索引
SET @sql = (SELECT IF(
    @table_exists > 0 AND
    (SELECT COUNT(*) FROM information_schema.statistics
     WHERE table_schema = DATABASE()
     AND table_name = 'license_keys'
     AND index_name = 'idx_last_heartbeat') = 0,
    'ALTER TABLE `license_keys` ADD INDEX `idx_last_heartbeat` (`last_heartbeat`)',
    'SELECT "idx_last_heartbeat索引已存在或表不存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加expiry_status复合索引
SET @sql = (SELECT IF(
    @table_exists > 0 AND
    (SELECT COUNT(*) FROM information_schema.statistics
     WHERE table_schema = DATABASE()
     AND table_name = 'license_keys'
     AND index_name = 'idx_expiry_status') = 0,
    'ALTER TABLE `license_keys` ADD INDEX `idx_expiry_status` (`expiry_date`, `status`)',
    'SELECT "idx_expiry_status索引已存在或表不存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示执行结果
SELECT
    'security_logs表创建完成' as '执行状态',
    'MySQL 5.7+ 兼容' as '兼容性',
    '安全日志功能已启用' as '功能状态',
    CASE
        WHEN @table_exists > 0 THEN 'license_keys表字段和索引已更新'
        ELSE 'license_keys表不存在，跳过字段添加'
    END as 'license_keys状态';
